// Mock the DynamoDB service
const mockDynamoService = {
  getUserById: jest.fn()
}

jest.mock('../services/dynamoService', () => {
  return jest.fn().mockImplementation(() => mockDynamoService)
})

const { handler } = require('../handlers/getCurrentUser')

describe('getCurrentUser Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockDynamoService.getUserById.mockReset()

    // Mock environment variables
    process.env.USERS_TABLE_NAME = 'test-users-table'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (userId = 'test-user-123') => ({
    requestContext: {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    }
  })

  describe('Success Cases', () => {
    test('should get current user successfully', async () => {
      const mockUser = {
        userId: 'test-user-123',
        username: 'testuser',
        email: '<EMAIL>',
        createdAt: '2024-01-01T00:00:00.000Z',
        bio: 'Test bio',
        isActive: true
      }

      mockDynamoService.getUserById.mockResolvedValue(mockUser)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.user).toEqual(mockUser)

      // Verify DynamoDB service was called with correct parameters
      expect(mockDynamoService.getUserById).toHaveBeenCalledWith('test-user-123')
    })

    test('should handle user not found', async () => {
      mockDynamoService.getUserById.mockResolvedValue(null)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('USER_NOT_FOUND')
    })
  })

  describe('Authentication Errors', () => {
    test('should reject unauthenticated requests', async () => {
      const event = {
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      mockDynamoService.getUserById.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('DATABASE_ERROR')
    })
  })
})
